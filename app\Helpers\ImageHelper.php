<?php

namespace App\Helpers;

use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Intervention\Image\ImageManager;
use Intervention\Image\Drivers\Gd\Driver;

class ImageHelper
{
    /**
     * Upload and resize image
     *
     * @param UploadedFile $file
     * @param string $folder
     * @param int $maxWidth
     * @param int $maxHeight
     * @return string
     */
    public static function uploadAndResize(UploadedFile $file, string $folder, int $maxWidth = 800, int $maxHeight = 600): string
    {
        // Generate unique filename
        $filename = time() . '_' . uniqid() . '.' . $file->getClientOriginalExtension();
        $path = $folder . '/' . $filename;

        // Create directory if not exists
        $fullPath = storage_path('app/public/' . $folder);
        if (!file_exists($fullPath)) {
            mkdir($fullPath, 0755, true);
        }

        // Store original file temporarily
        $tempPath = $file->store('temp', 'public');
        $fullTempPath = storage_path('app/public/' . $tempPath);

        // Resize image if needed
        try {
            $manager = new ImageManager(new Driver());
            $image = $manager->read($fullTempPath);

            // Get current dimensions
            $currentWidth = $image->width();
            $currentHeight = $image->height();

            // Resize if larger than max dimensions
            if ($currentWidth > $maxWidth || $currentHeight > $maxHeight) {
                $image->scale(width: $maxWidth, height: $maxHeight);
            }

            // Save resized image
            $image->save(storage_path('app/public/' . $path));

            // Delete temp file
            Storage::disk('public')->delete($tempPath);

            return $path;
        } catch (\Exception $e) {
            // If image processing fails, use original file
            Storage::disk('public')->delete($tempPath);
            return $file->store($folder, 'public');
        }
    }

    /**
     * Delete image file
     *
     * @param string|null $imagePath
     * @return bool
     */
    public static function deleteImage(?string $imagePath): bool
    {
        if ($imagePath && Storage::disk('public')->exists($imagePath)) {
            return Storage::disk('public')->delete($imagePath);
        }
        return false;
    }

    /**
     * Get image URL
     *
     * @param string|null $imagePath
     * @param string $default
     * @return string
     */
    public static function getImageUrl(?string $imagePath, string $default = '/images/no-image.svg'): string
    {
        if ($imagePath && Storage::disk('public')->exists($imagePath)) {
            return asset('storage/' . $imagePath);
        }
        return asset($default);
    }

    /**
     * Validate image file
     *
     * @param UploadedFile $file
     * @param int $maxSize (in KB)
     * @return array
     */
    public static function validateImage(UploadedFile $file, int $maxSize = 2048): array
    {
        $errors = [];

        // Check file type
        $allowedTypes = ['jpeg', 'jpg', 'png', 'gif', 'webp'];
        $extension = strtolower($file->getClientOriginalExtension());
        
        if (!in_array($extension, $allowedTypes)) {
            $errors[] = 'ไฟล์ต้องเป็นรูปภาพ (jpeg, jpg, png, gif, webp)';
        }

        // Check file size
        if ($file->getSize() > $maxSize * 1024) {
            $errors[] = "ขนาดไฟล์ต้องไม่เกิน {$maxSize} KB";
        }

        // Check if it's actually an image
        try {
            $imageInfo = getimagesize($file->getPathname());
            if (!$imageInfo) {
                $errors[] = 'ไฟล์ไม่ใช่รูปภาพที่ถูกต้อง';
            }
        } catch (\Exception $e) {
            $errors[] = 'ไม่สามารถอ่านไฟล์รูปภาพได้';
        }

        return $errors;
    }

    /**
     * Create thumbnail
     *
     * @param string $imagePath
     * @param int $width
     * @param int $height
     * @return string|null
     */
    public static function createThumbnail(string $imagePath, int $width = 150, int $height = 150): ?string
    {
        try {
            $fullPath = storage_path('app/public/' . $imagePath);

            if (!file_exists($fullPath)) {
                return null;
            }

            $pathInfo = pathinfo($imagePath);
            $thumbnailPath = $pathInfo['dirname'] . '/thumb_' . $pathInfo['basename'];
            $thumbnailFullPath = storage_path('app/public/' . $thumbnailPath);

            $manager = new ImageManager(new Driver());
            $image = $manager->read($fullPath);
            $image->cover($width, $height);
            $image->save($thumbnailFullPath);

            return $thumbnailPath;
        } catch (\Exception $e) {
            return null;
        }
    }
}
