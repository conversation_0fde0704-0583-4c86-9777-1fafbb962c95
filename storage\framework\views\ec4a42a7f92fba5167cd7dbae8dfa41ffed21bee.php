

<?php $__env->startSection('title', 'บทความ - ผู้ใหญ่จากบริการ'); ?>

<?php $__env->startSection('content'); ?>
<!-- Hero Section -->
<section class="hero-section text-white">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <h1 class="display-4 fw-bold mb-4">
                    <i class="fas fa-newspaper me-3"></i>บทความน่าสนใจ
                </h1>
                <p class="lead mb-4">อ่านบทความที่มีประโยชน์และอัปเดตข่าวสารล่าสุด</p>
                <a href="#articles" class="btn btn-light btn-lg px-4">
                    <i class="fas fa-arrow-down me-2"></i>อ่านบทความทั้งหมด
                </a>
            </div>
            <div class="col-lg-6 text-center">
                <i class="fas fa-newspaper fa-10x opacity-75"></i>
            </div>
        </div>
    </div>
</section>

<!-- Articles Section -->
<section id="articles" class="py-5">
    <div class="container">
        <div class="text-center mb-5">
            <h2 class="section-title">บทความล่าสุด</h2>
            <p class="text-muted lead">อัปเดตข่าวสารและความรู้ที่มีประโยชน์</p>
        </div>

        <?php if($articles->count() > 0): ?>
            <div class="row g-4">
                <?php $__currentLoopData = $articles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $article): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="col-lg-4 col-md-6">
                    <div class="card h-100 shadow-sm">
                        <?php if($article->image): ?>
                            <img src="<?php echo e(asset('storage/' . $article->image)); ?>" class="card-img-top" alt="<?php echo e($article->title); ?>" style="height: 200px; object-fit: cover;">
                        <?php else: ?>
                            <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 200px;">
                                <i class="fas fa-newspaper fa-4x text-muted"></i>
                            </div>
                        <?php endif; ?>
                        <div class="card-body d-flex flex-column">
                            <div class="mb-2">
                                <?php if($article->published_at): ?>
                                    <small class="text-muted">
                                        <i class="fas fa-calendar me-1"></i><?php echo e($article->published_at->format('d/m/Y')); ?>

                                    </small>
                                <?php endif; ?>
                            </div>
                            <h5 class="card-title fw-bold text-primary"><?php echo e(Str::limit($article->title, 60)); ?></h5>
                            <p class="card-text text-muted flex-grow-1"><?php echo e(Str::limit(strip_tags($article->content), 120)); ?></p>
                            <a href="<?php echo e(route('articles.show', $article)); ?>" class="btn btn-primary mt-auto">
                                <i class="fas fa-readme me-1"></i>อ่านเพิ่มเติม
                            </a>
                        </div>
                    </div>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        <?php else: ?>
            <div class="text-center py-5">
                <i class="fas fa-newspaper fa-5x text-muted mb-4"></i>
                <h3 class="text-muted">ยังไม่มีบทความ</h3>
                <p class="text-muted">เรากำลังเตรียมบทความที่น่าสนใจสำหรับคุณ</p>
            </div>
        <?php endif; ?>
    </div>
</section>

<!-- Newsletter Section -->
<section class="bg-light py-5">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h3 class="fw-bold mb-3">ติดตามข่าวสารล่าสุด</h3>
                <p class="lead mb-0">รับข่าวสารและบทความใหม่ๆ ทางอีเมล</p>
            </div>
            <div class="col-lg-4 text-lg-end">
                <a href="/contact" class="btn btn-primary btn-lg">
                    <i class="fas fa-envelope me-2"></i>ติดต่อเรา
                </a>
            </div>
        </div>
    </div>
</section>
<?php $__env->stopSection(); ?> 
<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\XAMPP\htdocs\SoloShop\resources\views/articles/index.blade.php ENDPATH**/ ?>