@extends('layouts.app')

@section('title', 'แก้ไขบริการ - Admin Panel')

@section('content')
<div class="container-fluid">
    <!-- Content Header -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">
                        <i class="fas fa-edit me-2"></i>แก้ไขบริการ
                    </h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">แดชบอร์ด</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('admin.services.index') }}">จัดการบริการ</a></li>
                        <li class="breadcrumb-item active">แก้ไขบริการ</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">แก้ไขข้อมูลบริการ</h3>
                        </div>
                        <div class="card-body">
                            @if($errors->any())
                                <div class="alert alert-danger">
                                    <ul class="mb-0">
                                        @foreach($errors->all() as $error)
                                            <li>{{ $error }}</li>
                                        @endforeach
                                    </ul>
                                </div>
                            @endif

                            <form action="{{ route('admin.services.update', $service) }}" method="POST" enctype="multipart/form-data">
                                @csrf @method('PUT')

                                <div class="row">
                                    <div class="col-md-8">
                                        <div class="form-group">
                                            <label for="title">ชื่อบริการ <span class="text-danger">*</span></label>
                                            <input type="text" name="title" id="title" class="form-control @error('title') is-invalid @enderror"
                                                   value="{{ old('title', $service->title) }}" required>
                                            @error('title')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <div class="form-group">
                                            <label for="description">รายละเอียดบริการ <span class="text-danger">*</span></label>
                                            <textarea name="description" id="description" rows="5"
                                                      class="form-control @error('description') is-invalid @enderror" required>{{ old('description', $service->description) }}</textarea>
                                            @error('description')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <div class="form-group">
                                            <label for="price">ราคา (บาท) <span class="text-danger">*</span></label>
                                            <input type="number" name="price" id="price" class="form-control @error('price') is-invalid @enderror"
                                                   step="0.01" min="0" value="{{ old('price', $service->price) }}" required>
                                            @error('price')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>

                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label for="image">รูปภาพบริการ</label>
                                            @if($service->image)
                                                <div class="mb-3">
                                                    <img src="{{ asset('storage/'.$service->image) }}"
                                                         class="img-thumbnail d-block"
                                                         style="max-width: 200px; max-height: 200px;">
                                                    <small class="text-muted">รูปภาพปัจจุบัน</small>
                                                </div>
                                            @endif
                                            <input type="file" name="image" id="image" class="form-control @error('image') is-invalid @enderror"
                                                   accept="image/jpeg,image/jpg,image/png,image/gif,image/webp">
                                            <small class="form-text text-muted">
                                                รองรับไฟล์: JPEG, JPG, PNG, GIF, WebP (ขนาดไม่เกิน 2MB)
                                            </small>
                                            @error('image')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group mt-4">
                                    <button type="submit" class="btn btn-success">
                                        <i class="fas fa-save me-1"></i>บันทึกการแก้ไข
                                    </button>
                                    <a href="{{ route('admin.services.index') }}" class="btn btn-secondary">
                                        <i class="fas fa-arrow-left me-1"></i>ย้อนกลับ
                                    </a>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>
@endsection