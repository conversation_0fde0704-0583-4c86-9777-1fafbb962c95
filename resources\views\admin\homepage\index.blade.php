@extends('layouts.app')
@section('content')
    <h1>เนื้อหาหน้าแรก (จัดการหลังบ้าน)</h1>
    <table class="table table-bordered">
        <thead>
            <tr>
                <th>Section</th>
                <th>หัวข้อ</th>
                <th>เนื้อหา</th>
                <th>รูปภาพ</th>
                <th>ปุ่ม</th>
                <th>จัดการ</th>
            </tr>
        </thead>
        <tbody>
            @foreach($homepage_contents as $content)
                <tr>
                    <td>{{ $content->section }}</td>
                    <td>{{ $content->title }}</td>
                    <td>{{ Str::limit($content->content, 50) }}</td>
                    <td>@if($content->image)<img src="{{ asset('storage/'.$content->image) }}" style="max-width:80px;">@endif</td>
                    <td>
                        @if($content->button_text)
                            <span class="badge bg-primary">{{ $content->button_text }}</span>
                        @endif
                    </td>
                    <td>
                        <a href="{{ route('admin.homepage.edit', $content) }}" class="btn btn-sm btn-warning">แก้ไข</a>
                    </td>
                </tr>
            @endforeach
        </tbody>
    </table>
@endsection 